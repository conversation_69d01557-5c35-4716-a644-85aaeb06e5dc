# 结直肠癌筛查微观模拟模型 桌面应用架构文档

## 介绍

本文档概述了结直肠癌筛查微观模拟模型桌面应用的完整架构，包括核心模拟引擎、用户界面、数据管理和系统集成。它作为AI驱动开发的单一真实来源，确保整个应用的一致性和可维护性。

这个架构专门为跨平台桌面应用设计，支持Windows、macOS和Linux操作系统，为医疗政策研究人员、流行病学专家和卫生经济学家提供专业的科学计算工具。

### 项目基础

**项目类型**：新建科学计算桌面应用（Greenfield项目）
**架构模式**：模块化单体架构 + 插件系统
**部署策略**：原生桌面应用打包 + 跨平台分发
**目标用户**：医疗政策研究人员、公共卫生官员、流行病学专家、卫生经济学家

### 代码仓库结构

**结构选择**：Monorepo（单一仓库）
**包管理工具**：Poetry（Python依赖管理）
**模块组织策略**：按功能域分离（模拟引擎、桌面界面、数据管理、机器学习）

选择Monorepo的原因：
1. 模拟引擎、桌面界面、数据处理模块之间需要共享复杂的数据结构
2. 科学计算代码需要在不同模块间共享算法实现
3. 统一的构建和打包流程，便于跨平台部署
4. 代码复用和依赖管理的简化
5. 桌面应用的紧密集成需求
6. 支持双重疾病进展通路（腺瘤-癌变85%、锯齿状腺瘤15%）的复杂建模需求

### 高层架构图

```mermaid
graph TB
    subgraph "用户层"
        U1[医疗政策制定者]
        U2[流行病学专家]
        U3[卫生经济学家]
        U4[公共卫生官员]
    end

    subgraph "桌面应用层"
        APP[桌面应用主程序<br/>PyQt6/PySide6]
        UI[用户界面模块<br/>科学研究级界面]
        WIZARD[配置向导<br/>6步模拟设置]
        MONITOR[监控界面<br/>实时进度跟踪]
    end

    subgraph "业务逻辑层"
        CONTROLLER[应用控制器<br/>业务流程协调]
        VALIDATOR[参数验证器<br/>实时验证反馈]
        WORKFLOW[工作流引擎<br/>模拟流程管理]
    end

    subgraph "核心模拟引擎层"
        SIM[微观模拟引擎<br/>人群队列管理]
        DISEASE[疾病建模模块<br/>双通路进展]
        SCREENING[筛查策略模块<br/>多工具支持]
        ECONOMICS[经济分析模块<br/>QALY/ICER计算]
    end

    subgraph "机器学习层"
        ML[校准引擎<br/>深度神经网络]
        SAMPLING[参数抽样<br/>拉丁超立方抽样]
        OPTIMIZATION[参数优化<br/>10,000组合生成]
        CONFIDENCE[置信区间<br/>95%置信区间计算]
    end

    subgraph "数据管理层"
        DATA[数据管理器<br/>多格式支持]
        IMPORT[数据导入<br/>CSV/Excel/JSON]
        VALIDATION[数据验证<br/>质量检查]
        VERSION[版本控制<br/>数据历史管理]
    end

    subgraph "计算资源层"
        PARALLEL[并行计算<br/>多进程/多线程]
        GPU[GPU加速<br/>CUDA/OpenCL]
        MEMORY[内存管理<br/>大规模数据处理]
        PERFORMANCE[性能监控<br/>资源使用跟踪]
    end

    subgraph "存储层"
        SQLITE[SQLite数据库<br/>本地数据存储]
        FILES[文件系统<br/>项目文件管理]
        CACHE[缓存系统<br/>计算结果缓存]
        BACKUP[备份系统<br/>自动备份恢复]
    end

    subgraph "系统集成层"
        OS[操作系统集成<br/>跨平台支持]
        EXPORT[结果导出<br/>多格式报告]
        NOTIFICATION[通知系统<br/>进度通知]
        LOGGING[日志系统<br/>调试审计]
    end

    %% 用户交互
    U1 --> APP
    U2 --> APP
    U3 --> APP
    U4 --> APP

    %% 应用层内部
    APP --> UI
    APP --> WIZARD
    APP --> MONITOR
    UI --> CONTROLLER
    WIZARD --> CONTROLLER
    MONITOR --> CONTROLLER

    %% 业务逻辑层
    CONTROLLER --> VALIDATOR
    CONTROLLER --> WORKFLOW
    WORKFLOW --> SIM

    %% 核心引擎层
    SIM --> DISEASE
    SIM --> SCREENING
    SIM --> ECONOMICS
    DISEASE --> ML
    SCREENING --> ML
    ECONOMICS --> ML

    %% 机器学习层
    ML --> SAMPLING
    ML --> OPTIMIZATION
    ML --> CONFIDENCE

    %% 数据管理
    CONTROLLER --> DATA
    DATA --> IMPORT
    DATA --> VALIDATION
    DATA --> VERSION

    %% 计算资源
    SIM --> PARALLEL
    ML --> PARALLEL
    PARALLEL --> GPU
    PARALLEL --> MEMORY
    PARALLEL --> PERFORMANCE

    %% 存储访问
    SIM --> SQLITE
    DATA --> SQLITE
    ML --> SQLITE
    CONTROLLER --> FILES
    SIM --> CACHE
    DATA --> BACKUP

    %% 系统集成
    APP --> OS
    CONTROLLER --> EXPORT
    APP --> NOTIFICATION
    APP --> LOGGING
```

## 技术栈

### 技术栈表

| 类别 | 技术 | 版本 | 用途 | 选择理由 |
|------|------|------|------|----------|
| 应用语言 | Python | 3.9+ | 桌面应用开发和科学计算 | 丰富的科学计算库，NumPy/SciPy生态系统，跨平台支持 |
| GUI框架 | PyQt6/PySide6 | 6.4+ | 桌面用户界面 | 成熟的跨平台GUI框架，原生外观，丰富的控件，支持科学研究级界面 |
| 备选GUI | Tkinter | 内置 | 轻量级界面选项 | Python内置，无额外依赖，适合简单界面 |
| 数据可视化 | Matplotlib + Seaborn | 3.6+ / 0.12+ | 科学图表和数据可视化 | 科学计算标准，丰富的图表类型，高质量输出，支持大规模数据 |
| 交互式图表 | PyQtGraph | 0.13+ | 实时数据可视化 | 高性能交互式图表，适合大数据集，支持100万个体数据可视化 |
| 科学计算 | NumPy + SciPy | 1.24+ / 1.10+ | 数值计算和科学算法 | 科学计算基础库，高性能数组操作，支持大规模人群模拟 |
| 数据处理 | Pandas | 1.5+ | 数据分析和处理 | 强大的数据操作能力，支持多种数据格式（CSV、Excel、JSON） |
| 机器学习 | TensorFlow/PyTorch | 2.12+ / 2.0+ | 深度学习和模型训练 | 成熟的深度学习框架，GPU加速支持，支持拉丁超立方抽样 |
| 本地数据库 | SQLite | 3.40+ | 本地数据存储 | 轻量级嵌入式数据库，无需服务器，ACID事务，适合桌面应用 |
| 数据库ORM | SQLAlchemy | 2.0+ | 数据库抽象层 | 强大的ORM，支持复杂查询，类型安全 |
| 配置管理 | PyYAML + ConfigParser | 6.0+ / 内置 | 应用配置管理 | 支持YAML和INI格式，易于维护，支持模拟参数配置 |
| 日志系统 | Python Logging | 内置 | 应用日志记录 | Python内置，灵活的日志配置，多种输出格式，支持调试审计 |
| 测试框架 | pytest + pytest-qt | 7.0+ / 4.2+ | 单元测试和GUI测试 | 强大的测试框架，支持GUI组件测试，科学计算验证 |
| 代码质量 | Black + Flake8 + mypy | Latest | 代码格式化和检查 | 自动格式化，代码质量检查，类型检查 |
| 打包工具 | PyInstaller | 5.0+ | 应用打包和分发 | 创建独立可执行文件，跨平台支持，适合桌面应用分发 |
| 备选打包 | cx_Freeze | 6.0+ | 替代打包方案 | 轻量级打包工具，适合简单应用 |
| 依赖管理 | Poetry | 1.4+ | Python依赖管理 | 现代依赖管理，虚拟环境集成，锁定文件 |
| 版本控制 | Git | 2.40+ | 代码版本管理 | 分布式版本控制，丰富的分支策略 |
| CI/CD | GitHub Actions | Latest | 持续集成和自动化 | 自动化测试、构建和发布流程，支持跨平台构建 |
| 多线程 | Python Threading | 内置 | 并发任务处理 | 适合I/O密集型任务，简单的并发模型，支持GUI响应性 |
| 多进程 | Python Multiprocessing | 内置 | CPU密集型计算 | 真正的并行计算，适合科学计算任务，支持大规模模拟 |
| GPU计算 | CUDA/OpenCL | Latest | 高性能计算加速 | GPU并行计算，大幅提升计算性能，支持机器学习校准 |
| 文档生成 | Sphinx | 5.0+ | 技术文档生成 | Python标准文档工具，支持多种输出格式 |
| 性能分析 | cProfile + memory_profiler | 内置 / Latest | 性能和内存分析 | 识别性能瓶颈，优化内存使用，支持大规模数据处理监控 |
| 统计分析 | Statsmodels | 0.14+ | 统计建模和分析 | 专业统计分析库，支持置信区间计算和敏感性分析 |
| 数据导入导出 | openpyxl + xlsxwriter | Latest | Excel文件处理 | 支持Excel格式的数据导入导出，适合医疗数据交换 |

### 架构决策记录

**ADR-001: 选择Python作为应用开发语言**
- 决策：使用Python 3.9+作为主要应用开发语言
- 理由：丰富的科学计算生态系统（NumPy、SciPy、Pandas、TensorFlow），成熟的GUI框架支持，适合复杂的微观模拟建模
- 后果：需要关注性能优化，使用多进程和GPU加速关键计算，支持100万个体100年周期模拟

**ADR-002: 采用桌面应用架构而非Web应用**
- 决策：开发跨平台桌面应用而非Web应用
- 理由：科学计算需要本地高性能处理，离线使用需求，数据安全考虑，支持大规模长期模拟（100年周期）
- 后果：需要处理跨平台兼容性，打包分发复杂度增加，但获得更好的计算性能和数据安全性

**ADR-003: 选择PyQt6作为主要GUI框架**
- 决策：使用PyQt6/PySide6构建用户界面
- 理由：成熟稳定，跨平台支持好，丰富的控件库，原生外观，支持科学研究级界面设计
- 后果：学习曲线较陡，许可证考虑（商业使用需付费），但提供专业的科学计算界面体验

**ADR-004: 使用SQLite作为本地数据库**
- 决策：使用SQLite作为主要数据存储
- 理由：轻量级，无需服务器，ACID事务支持，适合单机应用，支持大规模模拟结果存储
- 后果：并发写入限制，大数据集性能考虑，需要优化查询和索引策略

**ADR-005: 采用模块化单体架构**
- 决策：使用模块化单体架构而非微服务
- 理由：桌面应用的紧密集成需求，简化部署和维护，支持复杂的疾病建模和筛查策略配置
- 后果：需要设计清晰的模块边界，避免紧耦合，确保模块化扩展能力

**ADR-006: 实现双重疾病进展通路**
- 决策：支持腺瘤-癌变通路（85%）和锯齿状腺瘤通路（15%）
- 理由：基于最新医学研究，提高模型的科学准确性，符合结直肠癌发病机制
- 后果：增加模型复杂度，需要更多的参数配置和验证，但提供更准确的疾病建模

**ADR-007: 集成机器学习校准系统**
- 决策：使用深度神经网络和拉丁超立方抽样进行参数校准
- 理由：提供自动化的参数优化，生成10,000个参数组合，计算95%置信区间
- 后果：增加计算复杂度和GPU依赖，但大幅提升校准精度和效率

**ADR-008: 支持多种数据格式导入**
- 决策：支持CSV、Excel、JSON等多种数据格式
- 理由：适应医疗数据的多样性，提高数据互操作性，支持生命表、基准值等数据导入
- 后果：需要实现多种数据解析器和验证器，增加开发复杂度

## 变更日志

| 版本 | 日期 | 变更内容 | 作者 |
|------|------|----------|------|
| 3.0 | 2025-01-31 | 根据PRD和前端设计文档更新架构，增加疾病建模、机器学习校准等专项功能 | 架构师 Winston |
| 2.0 | 2025-01-31 | 将Web应用架构重构为桌面应用架构 | 架构师 Winston |
| 1.0 | 2025-01-31 | 初始全栈架构设计 | 架构师 Winston |

## 模块接口规格说明

### 核心模块接口

桌面应用采用模块化架构，各模块通过定义良好的Python接口进行交互。以下是主要模块的接口规格：

#### 1. 模拟引擎接口 (SimulationEngine)

```python
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Iterator
from dataclasses import dataclass
from enum import Enum

@dataclass
class PopulationConfig:
    """人群配置参数"""
    size: int                           # 人群规模（1,000-1,000,000）
    age_distribution: Dict[str, int]    # 年龄分布 {min_age: 18, max_age: 100}
    gender_ratio: float                 # 性别比例 (0-1)
    risk_factors: Dict[str, float]      # 风险因素分布

@dataclass
class DiseaseModelConfig:
    """疾病建模配置"""
    adenoma_pathway_rate: float = 0.85  # 腺瘤-癌变通路比例
    serrated_pathway_rate: float = 0.15 # 锯齿状腺瘤通路比例
    progression_parameters: Dict        # 进展参数

@dataclass
class ScreeningStrategyConfig:
    """筛查策略配置"""
    tools: List[Dict]                   # 筛查工具列表
    start_age: int                      # 开始年龄
    end_age: int                        # 结束年龄
    intervals: Dict[str, int]           # 筛查间隔
    compliance_rates: Dict[str, float]  # 依从性率
    follow_up_compliance_rates: Dict[str, float]  # 随访依从性率


@dataclass
class EconomicConfig:
    """经济分析配置"""
    discount_rate: float = 0.03         # 年度折现率（3%）
    costs: Dict[str, float]             # 成本参数
    utilities: Dict[str, float]         # 效用值
    time_horizon: int = 100             # 时间范围（年）

class SimulationStatus(Enum):
    """模拟状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class SimulationProgress:
    """模拟进度信息"""
    status: SimulationStatus
    current_year: int
    total_years: int
    individuals_processed: int
    total_individuals: int
    progress_percentage: float
    estimated_completion: Optional[str]
    resource_usage: Dict[str, float]

class SimulationEngine(ABC):
    """模拟引擎抽象接口"""

    @abstractmethod
    def initialize_population(self, config: PopulationConfig) -> bool:
        """初始化人群队列"""
        pass

    @abstractmethod
    def configure_disease_model(self, config: DiseaseModelConfig) -> bool:
        """配置疾病建模参数"""
        pass

    @abstractmethod
    def configure_screening_strategy(self, config: ScreeningStrategyConfig) -> bool:
        """配置筛查策略"""
        pass

    @abstractmethod
    def configure_economic_analysis(self, config: EconomicConfig) -> bool:
        """配置经济分析参数"""
        pass

    @abstractmethod
    def start_simulation(self) -> str:
        """启动模拟，返回模拟ID"""
        pass

    @abstractmethod
    def pause_simulation(self, simulation_id: str) -> bool:
        """暂停模拟"""
        pass

    @abstractmethod
    def resume_simulation(self, simulation_id: str) -> bool:
        """恢复模拟"""
        pass

    @abstractmethod
    def stop_simulation(self, simulation_id: str) -> bool:
        """停止模拟"""
        pass

    @abstractmethod
    def get_simulation_progress(self, simulation_id: str) -> SimulationProgress:
        """获取模拟进度"""
        pass

    @abstractmethod
    def get_simulation_results(self, simulation_id: str) -> Dict:
        """获取模拟结果"""
        pass
```

#### 2. 机器学习校准接口 (CalibrationEngine)

```python
@dataclass
class CalibrationConfig:
    """校准配置参数"""
    sampling_method: str = "latin_hypercube"    # 抽样方法
    sample_size: int = 10000                    # 样本数量
    network_architecture: Dict                  # 神经网络架构
    training_parameters: Dict                   # 训练参数
    convergence_criteria: Dict                  # 收敛标准

@dataclass
class CalibrationTarget:
    """校准目标"""
    target_type: str                           # 目标类型（腺瘤患病率、癌症发病率等）
    age_group: str                             # 年龄组
    gender: str                                # 性别
    value: float                               # 目标值
    confidence_interval: tuple                 # 置信区间
    weight: float                              # 权重

class CalibrationStatus(Enum):
    """校准状态"""
    NOT_STARTED = "not_started"
    SAMPLING = "sampling"
    TRAINING = "training"
    CONVERGED = "converged"
    FAILED = "failed"

@dataclass
class CalibrationProgress:
    """校准进度"""
    status: CalibrationStatus
    current_epoch: int
    total_epochs: int
    training_loss: float
    validation_loss: float
    convergence_score: float
    estimated_completion: Optional[str]

class CalibrationEngine(ABC):
    """机器学习校准引擎接口"""

    @abstractmethod
    def configure_calibration(self, config: CalibrationConfig) -> bool:
        """配置校准参数"""
        pass

    @abstractmethod
    def set_calibration_targets(self, targets: List[CalibrationTarget]) -> bool:
        """设置校准目标"""
        pass

    @abstractmethod
    def generate_parameter_samples(self) -> List[Dict]:
        """生成参数样本（拉丁超立方抽样）"""
        pass

    @abstractmethod
    def start_calibration(self) -> str:
        """启动校准过程"""
        pass

    @abstractmethod
    def get_calibration_progress(self, job_id: str) -> CalibrationProgress:
        """获取校准进度"""
        pass

    @abstractmethod
    def get_calibration_results(self, job_id: str) -> Dict:
        """获取校准结果和置信区间"""
        pass

    @abstractmethod
    def save_calibration_model(self, job_id: str, path: str) -> bool:
        """保存校准模型"""
        pass
```

#### 3. 数据管理接口 (DataManager)

```python
from enum import Enum
from pathlib import Path

class DataType(Enum):
    """数据类型枚举"""
    LIFE_TABLES = "life_tables"
    CALIBRATION_TARGETS = "calibration_targets"
    POPULATION_DATA = "population_data"
    SCREENING_PARAMETERS = "screening_parameters"

class FileFormat(Enum):
    """文件格式枚举"""
    CSV = "csv"
    EXCEL = "excel"
    JSON = "json"

@dataclass
class DataValidationResult:
    """数据验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]
    quality_score: float

@dataclass
class DataImportResult:
    """数据导入结果"""
    success: bool
    records_imported: int
    validation_result: DataValidationResult
    file_id: str

class DataManager(ABC):
    """数据管理器接口"""

    @abstractmethod
    def import_data(self, file_path: Path, data_type: DataType,
                   format: FileFormat) -> DataImportResult:
        """导入数据文件"""
        pass

    @abstractmethod
    def validate_data(self, data: Dict, data_type: DataType) -> DataValidationResult:
        """验证数据质量"""
        pass

    @abstractmethod
    def get_data_history(self, data_type: DataType) -> List[Dict]:
        """获取数据版本历史"""
        pass

    @abstractmethod
    def compare_data_versions(self, version1: str, version2: str) -> Dict:
        """比较数据版本"""
        pass

    @abstractmethod
    def rollback_data(self, data_type: DataType, version: str) -> bool:
        """回滚数据到指定版本"""
        pass

    @abstractmethod
    def export_data(self, data_type: DataType, format: FileFormat,
                   output_path: Path) -> bool:
        """导出数据"""
        pass
```

#### 4. 经济分析接口 (EconomicAnalyzer)

```python
@dataclass
class CostParameters:
    """成本参数"""
    screening_costs: Dict[str, float]          # 筛查成本
    treatment_costs: Dict[str, float]          # 治疗成本
    indirect_costs: Dict[str, float]           # 间接成本
    discount_rate: float = 0.03                # 折现率

@dataclass
class HealthOutcomes:
    """健康结果指标"""
    qaly: float                                # 质量调整生命年
    lyg: float                                 # 挽救生命年
    life_expectancy: float                     # 预期寿命
    cancer_cases_prevented: int                # 预防的癌症病例数
    deaths_prevented: int                      # 预防的死亡数

@dataclass
class EconomicResults:
    """经济分析结果"""
    total_costs: float                         # 总成本
    total_benefits: float                      # 总效益
    icer: float                                # 增量成本效益比
    net_health_benefit: float                  # 净健康效益
    cost_effectiveness_ratio: float            # 成本效益比

class EconomicAnalyzer(ABC):
    """经济分析器接口"""

    @abstractmethod
    def configure_costs(self, costs: CostParameters) -> bool:
        """配置成本参数"""
        pass

    @abstractmethod
    def calculate_health_outcomes(self, simulation_results: Dict) -> HealthOutcomes:
        """计算健康结果指标"""
        pass

    @abstractmethod
    def perform_cost_effectiveness_analysis(self,
                                          baseline_results: Dict,
                                          intervention_results: Dict) -> EconomicResults:
        """执行成本效益分析"""
        pass

    @abstractmethod
    def sensitivity_analysis(self, parameters: List[str],
                           ranges: Dict[str, tuple]) -> Dict:
        """敏感性分析"""
        pass

    @abstractmethod
    def generate_economic_report(self, results: EconomicResults) -> str:
        """生成经济分析报告"""
        pass
```

#### 5. 用户界面接口 (UIController)

```python
from typing import Callable, Any
from PyQt6.QtCore import QObject, pyqtSignal

class UIController(QObject):
    """用户界面控制器"""

    # 信号定义
    simulation_started = pyqtSignal(str)       # 模拟开始信号
    simulation_progress = pyqtSignal(str, float)  # 进度更新信号
    simulation_completed = pyqtSignal(str)     # 模拟完成信号
    error_occurred = pyqtSignal(str, str)      # 错误发生信号

    @abstractmethod
    def show_main_dashboard(self) -> None:
        """显示主仪表板"""
        pass

    @abstractmethod
    def show_configuration_wizard(self, project_id: Optional[str] = None) -> None:
        """显示配置向导"""
        pass

    @abstractmethod
    def show_simulation_monitor(self, simulation_id: str) -> None:
        """显示模拟监控界面"""
        pass

    @abstractmethod
    def show_results_analysis(self, project_id: str) -> None:
        """显示结果分析界面"""
        pass

    @abstractmethod
    def show_data_management(self) -> None:
        """显示数据管理界面"""
        pass

    @abstractmethod
    def show_calibration_interface(self) -> None:
        """显示校准界面"""
        pass

    @abstractmethod
    def update_progress(self, simulation_id: str, progress: float) -> None:
        """更新进度显示"""
        pass

    @abstractmethod
    def show_error_dialog(self, title: str, message: str) -> None:
        """显示错误对话框"""
        pass

    @abstractmethod
    def show_notification(self, message: str, duration: int = 3000) -> None:
        """显示通知消息"""
        pass
```

#### 6. 事件系统接口

```python
from typing import Dict, List, Callable
from enum import Enum

class EventType(Enum):
    """事件类型"""
    SIMULATION_STARTED = "simulation_started"
    SIMULATION_PROGRESS = "simulation_progress"
    SIMULATION_COMPLETED = "simulation_completed"
    SIMULATION_FAILED = "simulation_failed"
    CALIBRATION_STARTED = "calibration_started"
    CALIBRATION_COMPLETED = "calibration_completed"
    DATA_IMPORTED = "data_imported"
    DATA_VALIDATION_FAILED = "data_validation_failed"
    CONFIGURATION_CHANGED = "configuration_changed"

@dataclass
class Event:
    """事件数据结构"""
    event_type: EventType
    timestamp: str
    source: str
    data: Dict[str, Any]
    metadata: Optional[Dict] = None

class EventBus(ABC):
    """事件总线接口"""

    @abstractmethod
    def subscribe(self, event_type: EventType, callback: Callable[[Event], None]) -> str:
        """订阅事件"""
        pass

    @abstractmethod
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        pass

    @abstractmethod
    def publish(self, event: Event) -> None:
        """发布事件"""
        pass

    @abstractmethod
    def get_event_history(self, event_type: Optional[EventType] = None) -> List[Event]:
        """获取事件历史"""
        pass
```

## 数据库模式

### 本地数据库模式 (SQLite)

桌面应用使用SQLite作为本地数据库，存储项目配置、模拟结果和数据管理信息。

```sql
-- 项目表
CREATE TABLE projects (
    id TEXT PRIMARY KEY,                       -- UUID格式的项目ID
    name TEXT NOT NULL,                        -- 项目名称
    description TEXT,                          -- 项目描述
    status TEXT NOT NULL DEFAULT 'draft',     -- 项目状态：draft, running, completed, failed
    created_at TEXT NOT NULL,                  -- 创建时间（ISO格式）
    updated_at TEXT NOT NULL,                  -- 更新时间（ISO格式）
    population_size INTEGER NOT NULL DEFAULT 10000,    -- 人群规模
    simulation_years INTEGER NOT NULL DEFAULT 100,     -- 模拟年数
    config TEXT NOT NULL DEFAULT '{}',        -- 配置JSON字符串
    metadata TEXT                              -- 元数据JSON字符串
);

-- 模拟运行表
CREATE TABLE simulation_runs (
    id TEXT PRIMARY KEY,                       -- UUID格式的模拟ID
    project_id TEXT NOT NULL,                  -- 关联项目ID
    status TEXT NOT NULL DEFAULT 'not_started', -- 模拟状态
    started_at TEXT,                           -- 开始时间
    completed_at TEXT,                         -- 完成时间
    progress REAL DEFAULT 0.0,                 -- 进度（0.0-1.0）
    current_year INTEGER DEFAULT 0,            -- 当前模拟年份
    total_years INTEGER DEFAULT 100,           -- 总模拟年数
    individuals_processed INTEGER DEFAULT 0,   -- 已处理个体数
    total_individuals INTEGER DEFAULT 0,       -- 总个体数
    error_message TEXT,                        -- 错误信息
    config_snapshot TEXT NOT NULL,             -- 配置快照JSON
    resource_usage TEXT,                       -- 资源使用情况JSON
    estimated_completion TEXT,                 -- 预估完成时间
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 模拟结果表
CREATE TABLE simulation_results (
    id TEXT PRIMARY KEY,                       -- UUID格式的结果ID
    simulation_id TEXT NOT NULL,               -- 关联模拟ID
    result_type TEXT NOT NULL,                 -- 结果类型
    year INTEGER,                              -- 模拟年份
    age_group TEXT,                            -- 年龄组
    gender TEXT,                               -- 性别
    metric_name TEXT NOT NULL,                 -- 指标名称
    metric_value REAL NOT NULL,                -- 指标值
    confidence_lower REAL,                     -- 置信区间下限
    confidence_upper REAL,                     -- 置信区间上限
    created_at TEXT NOT NULL,                  -- 创建时间
    FOREIGN KEY (simulation_id) REFERENCES simulation_runs(id) ON DELETE CASCADE
);

-- 数据文件表
CREATE TABLE data_files (
    id TEXT PRIMARY KEY,                       -- UUID格式的文件ID
    name TEXT NOT NULL,                        -- 文件名
    description TEXT,                          -- 文件描述
    file_type TEXT NOT NULL,                   -- 文件类型
    file_path TEXT NOT NULL,                   -- 文件路径
    file_size INTEGER NOT NULL,                -- 文件大小（字节）
    content_type TEXT NOT NULL,                -- 内容类型
    checksum TEXT,                             -- 文件校验和
    metadata TEXT,                             -- 元数据JSON
    imported_at TEXT NOT NULL,                 -- 导入时间
    version INTEGER NOT NULL DEFAULT 1         -- 版本号
);

-- 生命表数据
CREATE TABLE life_tables (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    region TEXT NOT NULL,                      -- 地区
    year INTEGER NOT NULL,                     -- 年份
    gender TEXT NOT NULL,                      -- 性别
    age INTEGER NOT NULL,                      -- 年龄
    mortality_rate REAL NOT NULL,              -- 死亡率
    life_expectancy REAL,                      -- 预期寿命
    file_id TEXT,                              -- 关联文件ID
    created_at TEXT NOT NULL,                  -- 创建时间
    FOREIGN KEY (file_id) REFERENCES data_files(id),
    UNIQUE(region, year, gender, age)
);

-- 校准基准值表
CREATE TABLE calibration_targets (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    target_type TEXT NOT NULL,                 -- 目标类型
    region TEXT NOT NULL,                      -- 地区
    year INTEGER NOT NULL,                     -- 年份
    gender TEXT NOT NULL,                      -- 性别
    age_group TEXT NOT NULL,                   -- 年龄组
    value REAL NOT NULL,                       -- 目标值
    confidence_lower REAL,                     -- 置信区间下限
    confidence_upper REAL,                     -- 置信区间上限
    weight REAL DEFAULT 1.0,                   -- 校准权重
    source TEXT,                               -- 数据来源
    file_id TEXT,                              -- 关联文件ID
    created_at TEXT NOT NULL,                  -- 创建时间
    FOREIGN KEY (file_id) REFERENCES data_files(id)
);

-- 校准任务表
CREATE TABLE calibration_jobs (
    id TEXT PRIMARY KEY,                       -- UUID格式的任务ID
    project_id TEXT NOT NULL,                  -- 关联项目ID
    status TEXT NOT NULL DEFAULT 'not_started', -- 任务状态
    started_at TEXT,                           -- 开始时间
    completed_at TEXT,                         -- 完成时间
    progress REAL DEFAULT 0.0,                 -- 进度
    current_epoch INTEGER DEFAULT 0,           -- 当前训练轮次
    total_epochs INTEGER DEFAULT 1000,         -- 总训练轮次
    training_loss REAL,                        -- 训练损失
    validation_loss REAL,                      -- 验证损失
    convergence_score REAL,                    -- 收敛分数
    config TEXT NOT NULL,                      -- 配置JSON
    results TEXT,                              -- 结果JSON
    model_path TEXT,                           -- 模型文件路径
    error_message TEXT,                        -- 错误信息
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 筛查工具参数表
CREATE TABLE screening_tools (
    id TEXT PRIMARY KEY,                       -- UUID格式的工具ID
    name TEXT NOT NULL,                        -- 工具名称
    tool_type TEXT NOT NULL,                   -- 工具类型
    sensitivity TEXT NOT NULL,                 -- 敏感性参数JSON
    specificity TEXT NOT NULL,                 -- 特异性参数JSON
    cost REAL NOT NULL,                        -- 成本
    description TEXT,                          -- 描述
    is_active INTEGER DEFAULT 1,               -- 是否激活
    created_at TEXT NOT NULL,                  -- 创建时间
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 人群数据表
CREATE TABLE population_data (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    region TEXT NOT NULL,                      -- 地区
    year INTEGER NOT NULL,                     -- 年份
    age_group TEXT NOT NULL,                   -- 年龄组
    gender TEXT NOT NULL,                      -- 性别
    population_count INTEGER NOT NULL,         -- 人口数量
    risk_factor_prevalence TEXT,               -- 风险因素患病率JSON
    file_id TEXT,                              -- 关联文件ID
    created_at TEXT NOT NULL,                  -- 创建时间
    FOREIGN KEY (file_id) REFERENCES data_files(id)
);

-- 经济参数表
CREATE TABLE economic_parameters (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    parameter_type TEXT NOT NULL,              -- 参数类型
    category TEXT NOT NULL,                    -- 类别
    name TEXT NOT NULL,                        -- 参数名称
    value REAL NOT NULL,                       -- 参数值
    currency TEXT DEFAULT 'CNY',               -- 货币单位
    year INTEGER NOT NULL,                     -- 适用年份
    source TEXT,                               -- 数据来源
    confidence_interval TEXT,                  -- 置信区间JSON
    created_at TEXT NOT NULL,                  -- 创建时间
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 系统配置表
CREATE TABLE system_config (
    key TEXT PRIMARY KEY,                      -- 配置键
    value TEXT NOT NULL,                       -- 配置值
    description TEXT,                          -- 描述
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 事件日志表
CREATE TABLE event_logs (
    id TEXT PRIMARY KEY,                       -- UUID格式的日志ID
    event_type TEXT NOT NULL,                  -- 事件类型
    source TEXT NOT NULL,                      -- 事件源
    timestamp TEXT NOT NULL,                   -- 时间戳
    data TEXT,                                 -- 事件数据JSON
    metadata TEXT                              -- 元数据JSON
);

-- 创建索引
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_updated ON projects(updated_at);
CREATE INDEX idx_simulation_runs_project ON simulation_runs(project_id);
CREATE INDEX idx_simulation_runs_status ON simulation_runs(status);
CREATE INDEX idx_simulation_results_simulation ON simulation_results(simulation_id);
CREATE INDEX idx_simulation_results_type ON simulation_results(result_type);
CREATE INDEX idx_data_files_type ON data_files(file_type);
CREATE INDEX idx_life_tables_region_year ON life_tables(region, year);
CREATE INDEX idx_calibration_targets_type ON calibration_targets(target_type);
CREATE INDEX idx_calibration_jobs_project ON calibration_jobs(project_id);
CREATE INDEX idx_calibration_jobs_status ON calibration_jobs(status);
CREATE INDEX idx_screening_tools_type ON screening_tools(tool_type);
CREATE INDEX idx_population_data_region ON population_data(region, year);
CREATE INDEX idx_economic_parameters_type ON economic_parameters(parameter_type);
CREATE INDEX idx_event_logs_type ON event_logs(event_type);
CREATE INDEX idx_event_logs_timestamp ON event_logs(timestamp);
```
    current_epoch INTEGER DEFAULT 0,           -- 当前训练轮次
    total_epochs INTEGER DEFAULT 1000,         -- 总训练轮次
    training_loss REAL,                        -- 训练损失
    validation_loss REAL,                      -- 验证损失
    convergence_score REAL,                    -- 收敛分数
    config TEXT NOT NULL,                      -- 配置JSON
    results TEXT,                              -- 结果JSON
    model_path TEXT,                           -- 模型文件路径
    error_message TEXT,                        -- 错误信息
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 筛查工具参数表
CREATE TABLE screening_tools (
    id TEXT PRIMARY KEY,                       -- UUID格式的工具ID
    name TEXT NOT NULL,                        -- 工具名称
    tool_type TEXT NOT NULL,                   -- 工具类型
    sensitivity TEXT NOT NULL,                 -- 敏感性参数JSON
    specificity TEXT NOT NULL,                 -- 特异性参数JSON
    cost REAL NOT NULL,                        -- 成本
    description TEXT,                          -- 描述
    is_active INTEGER DEFAULT 1,               -- 是否激活
    created_at TEXT NOT NULL,                  -- 创建时间
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 人群数据表
CREATE TABLE population_data (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    region TEXT NOT NULL,                      -- 地区
    year INTEGER NOT NULL,                     -- 年份
    age_group TEXT NOT NULL,                   -- 年龄组
    gender TEXT NOT NULL,                      -- 性别
    population_count INTEGER NOT NULL,         -- 人口数量
    risk_factor_prevalence TEXT,               -- 风险因素患病率JSON
    file_id TEXT,                              -- 关联文件ID
    created_at TEXT NOT NULL,                  -- 创建时间
    FOREIGN KEY (file_id) REFERENCES data_files(id)
);

-- 经济参数表
CREATE TABLE economic_parameters (
    id TEXT PRIMARY KEY,                       -- UUID格式的ID
    parameter_type TEXT NOT NULL,              -- 参数类型
    category TEXT NOT NULL,                    -- 类别
    name TEXT NOT NULL,                        -- 参数名称
    value REAL NOT NULL,                       -- 参数值
    currency TEXT DEFAULT 'CNY',               -- 货币单位
    year INTEGER NOT NULL,                     -- 适用年份
    source TEXT,                               -- 数据来源
    confidence_interval TEXT,                  -- 置信区间JSON
    created_at TEXT NOT NULL,                  -- 创建时间
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 系统配置表
CREATE TABLE system_config (
    key TEXT PRIMARY KEY,                      -- 配置键
    value TEXT NOT NULL,                       -- 配置值
    description TEXT,                          -- 描述
    updated_at TEXT NOT NULL                   -- 更新时间
);

-- 事件日志表
CREATE TABLE event_logs (
    id TEXT PRIMARY KEY,                       -- UUID格式的日志ID
    event_type TEXT NOT NULL,                  -- 事件类型
    source TEXT NOT NULL,                      -- 事件源
    timestamp TEXT NOT NULL,                   -- 时间戳
    data TEXT,                                 -- 事件数据JSON
    metadata TEXT                              -- 元数据JSON
);

-- 创建索引
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_updated ON projects(updated_at);
CREATE INDEX idx_simulation_runs_project ON simulation_runs(project_id);
CREATE INDEX idx_simulation_runs_status ON simulation_runs(status);
CREATE INDEX idx_simulation_results_simulation ON simulation_results(simulation_id);
CREATE INDEX idx_simulation_results_type ON simulation_results(result_type);
CREATE INDEX idx_data_files_type ON data_files(file_type);
CREATE INDEX idx_life_tables_region_year ON life_tables(region, year);
CREATE INDEX idx_calibration_targets_type ON calibration_targets(target_type);
CREATE INDEX idx_calibration_jobs_project ON calibration_jobs(project_id);
CREATE INDEX idx_calibration_jobs_status ON calibration_jobs(status);
CREATE INDEX idx_screening_tools_type ON screening_tools(tool_type);
CREATE INDEX idx_population_data_region ON population_data(region, year);
CREATE INDEX idx_economic_parameters_type ON economic_parameters(parameter_type);
CREATE INDEX idx_event_logs_type ON event_logs(event_type);
CREATE INDEX idx_event_logs_timestamp ON event_logs(timestamp);
```

### 本地文件存储结构

桌面应用使用本地文件系统存储数据文件、模拟结果和模型文件：

```
用户数据目录/
├── ColorectalScreeningModel/
│   ├── projects/                      # 项目文件
│   │   ├── {project_id}/
│   │   │   ├── config.json            # 项目配置
│   │   │   ├── metadata.json          # 项目元数据
│   │   │   ├── simulations/           # 模拟结果
│   │   │   │   ├── {simulation_id}/
│   │   │   │   │   ├── summary.json   # 结果摘要
│   │   │   │   │   ├── detailed_results.csv    # 详细结果
│   │   │   │   │   ├── adenoma_prevalence.csv  # 腺瘤患病率
│   │   │   │   │   ├── cancer_incidence.csv    # 癌症发病率
│   │   │   │   │   ├── mortality_rates.csv     # 死亡率
│   │   │   │   │   ├── economic_results.csv    # 经济分析结果
│   │   │   │   │   ├── performance_metrics.json # 性能指标
│   │   │   │   │   └── charts/         # 图表文件
│   │   │   │   │       ├── prevalence_trend.png
│   │   │   │   │       ├── cost_effectiveness.png
│   │   │   │   │       └── sensitivity_analysis.png
│   │   │   │   └── logs/               # 模拟日志
│   │   │   │       ├── simulation.log
│   │   │   │       └── error.log
│   │   │   └── calibration/           # 校准结果
│   │   │       ├── {job_id}/
│   │   │       │   ├── model.h5       # 训练好的模型
│   │   │       │   ├── parameters.json # 最优参数
│   │   │       │   ├── confidence_intervals.csv # 置信区间
│   │   │       │   ├── training_history.csv     # 训练历史
│   │   │       │   └── convergence_plot.png     # 收敛图
│   │   │       └── logs/              # 校准日志
│   │   └── templates/                 # 项目模板
│   │       ├── default_screening.json
│   │       ├── china_population.json
│   │       └── economic_baseline.json
│   ├── data/                          # 数据文件
│   │   ├── life_tables/               # 生命表数据
│   │   │   ├── china_national/
│   │   │   │   ├── 2020_male.csv
│   │   │   │   ├── 2020_female.csv
│   │   │   │   └── metadata.json
│   │   │   ├── provinces/
│   │   │   │   ├── beijing/
│   │   │   │   ├── shanghai/
│   │   │   │   └── guangdong/
│   │   │   └── cities/
│   │   ├── calibration_targets/       # 校准基准值
│   │   │   ├── adenoma_prevalence/
│   │   │   │   ├── china_2020.csv
│   │   │   │   └── literature_review.csv
│   │   │   ├── cancer_incidence/
│   │   │   │   ├── seer_data.csv
│   │   │   │   └── china_cancer_registry.csv
│   │   │   └── mortality_rates/
│   │   │       ├── who_data.csv
│   │   │       └── china_cdc.csv
│   │   ├── population/                # 人口结构数据
│   │   │   ├── demographics/
│   │   │   │   ├── age_distribution.csv
│   │   │   │   └── gender_ratio.csv
│   │   │   └── risk_factors/
│   │   │       ├── family_history.csv
│   │   │       ├── lifestyle_factors.csv
│   │   │       └── comorbidities.csv
│   │   ├── screening_tools/           # 筛查工具参数
│   │   │   ├── fit_parameters.json
│   │   │   ├── colonoscopy_params.json
│   │   │   ├── sigmoidoscopy_params.json
│   │   │   └── risk_assessment_tools.json
│   │   └── economic/                  # 经济参数
│   │       ├── costs/
│   │       │   ├── screening_costs.csv
│   │       │   ├── treatment_costs.csv
│   │       │   └── indirect_costs.csv
│   │       └── utilities/
│   │           ├── health_utilities.csv
│   │           └── quality_weights.csv
│   ├── exports/                       # 导出文件
│   │   ├── reports/
│   │   │   ├── {timestamp}_simulation_report.pdf
│   │   │   └── {timestamp}_comparison_analysis.pdf
│   │   ├── data/
│   │   │   ├── {timestamp}_results_export.csv
│   │   │   └── {timestamp}_parameters_export.json
│   │   └── charts/
│   │       ├── {timestamp}_cost_effectiveness.png
│   │       └── {timestamp}_sensitivity_analysis.png
│   ├── backups/                       # 备份文件
│   │   ├── {timestamp}/
│   │   │   ├── database_backup.db
│   │   │   ├── projects_backup.zip
│   │   │   └── config_backup.json
│   │   └── auto_backup/               # 自动备份
│   │       ├── daily/
│   │       ├── weekly/
│   │       └── monthly/
│   ├── logs/                          # 应用日志
│   │   ├── application.log
│   │   ├── error.log
│   │   ├── performance.log
│   │   └── user_activity.log
│   ├── cache/                         # 缓存文件
│   │   ├── computation_cache/
│   │   ├── visualization_cache/
│   │   └── data_cache/
│   ├── temp/                          # 临时文件
│   │   ├── uploads/
│   │   ├── processing/
│   │   └── downloads/
│   └── config/                        # 配置文件
│       ├── user_preferences.json
│       ├── system_settings.json
│       ├── database_config.json
│       └── logging_config.yaml
```

### 文件管理策略

**文件命名规范**：
- 使用UUID作为唯一标识符
- 时间戳格式：YYYY-MM-DD_HH-MM-SS
- 文件名包含版本信息和内容描述

**存储优化**：
- 大文件自动压缩存储
- 重复数据去重处理
- 定期清理临时文件和缓存
- 自动备份重要数据

**数据安全**：
- 重要文件自动备份
- 文件完整性校验（MD5/SHA256）
- 敏感数据加密存储
- 用户数据隔离保护

## 桌面应用界面架构

### PyQt6界面组织结构

桌面应用使用PyQt6构建科学研究级界面，采用模块化组件设计：

```python
src/gui/
├── main_window.py              # 主窗口类
├── components/                 # 可复用UI组件
│   ├── __init__.py
│   ├── base/                   # 基础组件
│   │   ├── custom_widget.py    # 自定义控件基类
│   │   ├── scientific_button.py # 科学界面按钮
│   │   ├── parameter_input.py   # 参数输入控件
│   │   └── validation_label.py  # 验证提示标签
│   ├── charts/                 # 图表组件
│   │   ├── scientific_plot.py   # 科学图表基类
│   │   ├── line_chart.py       # 线图组件
│   │   ├── bar_chart.py        # 柱状图组件
│   │   ├── heatmap.py          # 热力图组件
│   │   ├── scatter_plot.py     # 散点图组件
│   │   └── progress_chart.py   # 进度图表
│   ├── forms/                  # 表单组件
│   │   ├── parameter_form.py   # 参数配置表单
│   │   ├── data_upload_form.py # 数据上传表单
│   │   ├── validation_form.py  # 验证表单
│   │   └── wizard_page.py      # 向导页面基类
│   ├── tables/                 # 表格组件
│   │   ├── data_table.py       # 数据表格
│   │   ├── results_table.py    # 结果表格
│   │   ├── comparison_table.py # 比较表格
│   │   └── editable_table.py   # 可编辑表格
│   └── dialogs/                # 对话框组件
│       ├── progress_dialog.py  # 进度对话框
│       ├── error_dialog.py     # 错误对话框
│       ├── confirmation_dialog.py # 确认对话框
│       └── about_dialog.py     # 关于对话框
├── views/                      # 主要视图
│   ├── __init__.py
│   ├── dashboard/              # 主仪表板
│   │   ├── dashboard_view.py   # 仪表板主视图
│   │   ├── project_cards.py    # 项目卡片组件
│   │   ├── system_status.py    # 系统状态面板
│   │   └── quick_actions.py    # 快速操作面板
│   ├── configuration/          # 配置界面
│   │   ├── config_wizard.py    # 配置向导主控制器
│   │   ├── population_page.py  # 人群设置页面
│   │   ├── disease_page.py     # 疾病建模页面
│   │   ├── screening_page.py   # 筛查策略页面
│   │   ├── economic_page.py    # 经济参数页面
│   │   ├── calibration_page.py # 校准设置页面
│   │   └── summary_page.py     # 配置摘要页面
│   ├── monitoring/             # 监控界面
│   │   ├── simulation_monitor.py # 模拟监控主界面
│   │   ├── progress_panel.py   # 进度监控面板
│   │   ├── performance_panel.py # 性能监控面板
│   │   ├── log_viewer.py       # 日志查看器
│   │   └── resource_monitor.py # 资源监控器
│   ├── analysis/               # 分析界面
│   │   ├── results_dashboard.py # 结果仪表板
│   │   ├── comparison_view.py  # 比较分析视图
│   │   ├── visualization_panel.py # 可视化面板
│   │   ├── report_generator.py # 报告生成器
│   │   └── export_dialog.py    # 导出对话框
│   ├── data_management/        # 数据管理界面
│   │   ├── data_manager_view.py # 数据管理主视图
│   │   ├── import_wizard.py    # 数据导入向导
│   │   ├── quality_checker.py  # 数据质量检查器
│   │   ├── version_manager.py  # 版本管理器
│   │   └── file_browser.py     # 文件浏览器
│   └── calibration/            # 校准界面
│       ├── calibration_view.py # 校准主界面
│       ├── ml_config_panel.py  # 机器学习配置面板
│       ├── training_monitor.py # 训练监控器
│       ├── results_viewer.py   # 结果查看器
│       └── model_manager.py    # 模型管理器
├── styles/                     # 样式定义
│   ├── __init__.py
│   ├── themes.py               # 主题定义
│   ├── colors.py               # 颜色方案
│   ├── fonts.py                # 字体配置
│   └── stylesheets.py          # 样式表
├── utils/                      # 界面工具
│   ├── __init__.py
│   ├── layout_manager.py       # 布局管理器
│   ├── widget_factory.py       # 控件工厂
│   ├── icon_manager.py         # 图标管理器
│   ├── theme_manager.py        # 主题管理器
│   └── accessibility.py        # 无障碍访问支持
└── resources/                  # 资源文件
    ├── icons/                  # 图标文件
    ├── images/                 # 图片资源
    ├── stylesheets/            # CSS样式文件
    └── translations/           # 国际化文件
```

### 主窗口架构

```python
# main_window.py - 主窗口类示例
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QStackedWidget, QMenuBar,
                            QStatusBar, QToolBar, QSplitter)
from PyQt6.QtCore import QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon

class MainWindow(QMainWindow):
    """主窗口类 - 应用程序的主要界面容器"""

    # 信号定义
    project_changed = pyqtSignal(str)  # 项目切换信号
    view_changed = pyqtSignal(str)     # 视图切换信号

    def __init__(self):
        super().__init__()
        self.setWindowTitle("结直肠癌筛查微观模拟模型")
        self.setMinimumSize(1200, 800)

        # 初始化组件
        self._init_ui()
        self._init_menu_bar()
        self._init_tool_bar()
        self._init_status_bar()
        self._init_connections()

        # 应用主题
        self._apply_theme()

    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)

        # 创建分割器
        splitter = QSplitter()
        main_layout.addWidget(splitter)

        # 侧边栏
        self.sidebar = self._create_sidebar()
        splitter.addWidget(self.sidebar)

        # 主内容区域
        self.content_stack = QStackedWidget()
        splitter.addWidget(self.content_stack)

        # 设置分割器比例
        splitter.setSizes([250, 950])

        # 加载视图
        self._load_views()

    def _create_sidebar(self):
        """创建侧边栏导航"""
        from .components.navigation import NavigationSidebar
        return NavigationSidebar()

    def _load_views(self):
        """加载所有视图"""
        from .views.dashboard import DashboardView
        from .views.configuration import ConfigWizard
        from .views.monitoring import SimulationMonitor
        from .views.analysis import ResultsDashboard
        from .views.data_management import DataManagerView

        # 添加视图到堆栈
        self.content_stack.addWidget(DashboardView())
        self.content_stack.addWidget(ConfigWizard())
        self.content_stack.addWidget(SimulationMonitor())
        self.content_stack.addWidget(ResultsDashboard())
        self.content_stack.addWidget(DataManagerView())
```

### 状态管理架构

```python
# 应用状态管理 - 使用观察者模式
from PyQt6.QtCore import QObject, pyqtSignal
from typing import Dict, Any, Optional
import json

class ApplicationState(QObject):
    """应用程序状态管理器"""

    # 状态变更信号
    project_changed = pyqtSignal(dict)
    simulation_status_changed = pyqtSignal(str, str)
    configuration_updated = pyqtSignal(dict)
    data_imported = pyqtSignal(str, dict)

    def __init__(self):
        super().__init__()
        self._state = {
            'current_project': None,
            'simulation_config': {},
            'running_simulations': {},
            'calibration_jobs': {},
            'user_preferences': {},
            'system_status': {}
        }

    def get_state(self, key: str) -> Any:
        """获取状态值"""
        return self._state.get(key)

    def set_state(self, key: str, value: Any) -> None:
        """设置状态值并发出信号"""
        old_value = self._state.get(key)
        self._state[key] = value

        # 发出相应的信号
        if key == 'current_project' and value != old_value:
            self.project_changed.emit(value)
        elif key == 'simulation_config':
            self.configuration_updated.emit(value)

    def update_simulation_status(self, sim_id: str, status: str) -> None:
        """更新模拟状态"""
        if 'running_simulations' not in self._state:
            self._state['running_simulations'] = {}

        self._state['running_simulations'][sim_id] = status
        self.simulation_status_changed.emit(sim_id, status)

    def save_state(self, file_path: str) -> None:
        """保存状态到文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self._state, f, ensure_ascii=False, indent=2)

    def load_state(self, file_path: str) -> None:
        """从文件加载状态"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self._state = json.load(f)
        except FileNotFoundError:
            pass  # 使用默认状态

# 全局状态实例
app_state = ApplicationState()
```

## 部署架构

### 桌面应用打包和分发

#### 跨平台构建策略

桌面应用采用PyInstaller进行打包，支持Windows、macOS和Linux三大平台：

```python
# build_config.py - 构建配置
import os
import platform
from pathlib import Path

class BuildConfig:
    """构建配置管理"""

    # 应用基本信息
    APP_NAME = "ColorectalScreeningModel"
    APP_VERSION = "1.0.0"
    APP_DESCRIPTION = "结直肠癌筛查微观模拟模型"
    APP_AUTHOR = "医疗政策研究团队"

    # 构建目标
    PLATFORMS = {
        'windows': {
            'target': 'win32',
            'extension': '.exe',
            'icon': 'resources/icons/app.ico',
            'additional_files': [
                'resources/windows/*',
                'data/default/*'
            ]
        },
        'macos': {
            'target': 'darwin',
            'extension': '.app',
            'icon': 'resources/icons/app.icns',
            'additional_files': [
                'resources/macos/*',
                'data/default/*'
            ]
        },
        'linux': {
            'target': 'linux',
            'extension': '',
            'icon': 'resources/icons/app.png',
            'additional_files': [
                'resources/linux/*',
                'data/default/*'
            ]
        }
    }
```

#### PyInstaller配置和构建脚本

```python
# 构建配置继续
    # PyInstaller配置
    PYINSTALLER_CONFIG = {
        'name': APP_NAME,
        'onefile': False,  # 使用目录模式以提高启动速度
        'windowed': True,  # 无控制台窗口
        'optimize': 2,     # 字节码优化
        'strip': True,     # 去除调试信息
        'upx': True,       # UPX压缩
        'clean': True,     # 清理临时文件
        'noconfirm': True, # 不确认覆盖
        'hidden_imports': [
            'PyQt6.QtCore',
            'PyQt6.QtWidgets',
            'PyQt6.QtGui',
            'numpy',
            'pandas',
            'matplotlib',
            'tensorflow',
            'sklearn',
            'scipy',
            'sqlite3'
        ],
        'exclude_modules': [
            'tkinter',
            'unittest',
            'test',
            'distutils'
        ],
        'data_files': [
            ('data/life_tables', 'data/life_tables'),
            ('data/calibration_targets', 'data/calibration_targets'),
            ('resources/icons', 'resources/icons'),
            ('resources/stylesheets', 'resources/stylesheets'),
            ('config/default.yaml', 'config/default.yaml')
        ]
    }

# build_script.py - 构建脚本
import subprocess
import shutil
import zipfile
from pathlib import Path

def build_application(platform: str = None):
    """构建应用程序"""
    if platform is None:
        platform = get_current_platform()

    config = BuildConfig.PLATFORMS[platform]

    # 准备构建环境
    prepare_build_environment()

    # 执行PyInstaller构建
    build_command = create_build_command(platform, config)
    subprocess.run(build_command, check=True)

    # 后处理
    post_process_build(platform, config)

    # 创建安装包
    create_installer(platform, config)

def create_build_command(platform: str, config: dict) -> list:
    """创建构建命令"""
    cmd = [
        'pyinstaller',
        '--name', BuildConfig.APP_NAME,
        '--icon', config['icon'],
        '--windowed',
        '--onedir',  # 目录模式
        '--clean',
        '--noconfirm'
    ]

    # 添加隐藏导入
    for module in BuildConfig.PYINSTALLER_CONFIG['hidden_imports']:
        cmd.extend(['--hidden-import', module])

    # 添加数据文件
    for src, dst in BuildConfig.PYINSTALLER_CONFIG['data_files']:
        cmd.extend(['--add-data', f'{src}{os.pathsep}{dst}'])

    # 添加主程序文件
    cmd.append('src/main.py')

    return cmd
```

### 自动化构建流程

```yaml
# .github/workflows/build-desktop.yml
name: Build Desktop Application

on:
  push:
    tags:
      - 'v*'
  pull_request:
    branches: [ main ]

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry
        poetry install

    - name: Build Windows application
      run: |
        poetry run python scripts/build_desktop.py windows

    - name: Upload Windows artifact
      uses: actions/upload-artifact@v3
      with:
        name: windows-installer
        path: dist/*.exe

  build-macos:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry
        poetry install
        brew install create-dmg

    - name: Build macOS application
      run: |
        poetry run python scripts/build_desktop.py macos

    - name: Upload macOS artifact
      uses: actions/upload-artifact@v3
      with:
        name: macos-installer
        path: dist/*.dmg

  build-linux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install poetry
        poetry install
        sudo apt-get install -y fuse

    - name: Build Linux application
      run: |
        poetry run python scripts/build_desktop.py linux

    - name: Upload Linux artifact
      uses: actions/upload-artifact@v3
      with:
        name: linux-installer
        path: dist/*.AppImage

  release:
    needs: [build-windows, build-macos, build-linux]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-installer/*
          macos-installer/*
          linux-installer/*
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### 安装包创建

```python
def create_installer(platform: str, config: dict):
    """创建安装包"""
    if platform == 'windows':
        create_windows_installer()
    elif platform == 'macos':
        create_macos_installer()
    elif platform == 'linux':
        create_linux_installer()

def create_windows_installer():
    """创建Windows安装包（使用NSIS）"""
    nsis_script = '''
    !define APP_NAME "ColorectalScreeningModel"
    !define APP_VERSION "1.0.0"
    !define APP_PUBLISHER "医疗政策研究团队"
    !define APP_URL "https://github.com/medical-research/colorectal-screening"
    !define APP_SUPPORT_URL "https://github.com/medical-research/colorectal-screening/issues"

    Name "${APP_NAME}"
    OutFile "ColorectalScreeningModel-Setup-${APP_VERSION}.exe"
    InstallDir "$PROGRAMFILES\\${APP_NAME}"

    Section "MainSection" SEC01
        SetOutPath "$INSTDIR"
        File /r "dist\\ColorectalScreeningModel\\*"

        CreateDirectory "$SMPROGRAMS\\${APP_NAME}"
        CreateShortCut "$SMPROGRAMS\\${APP_NAME}\\${APP_NAME}.lnk" "$INSTDIR\\ColorectalScreeningModel.exe"
        CreateShortCut "$DESKTOP\\${APP_NAME}.lnk" "$INSTDIR\\ColorectalScreeningModel.exe"

        WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "DisplayName" "${APP_NAME}"
        WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APP_NAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
        WriteUninstaller "$INSTDIR\\uninstall.exe"
    SectionEnd
    '''

    with open('installer.nsi', 'w', encoding='utf-8') as f:
        f.write(nsis_script)

    subprocess.run(['makensis', 'installer.nsi'], check=True)

def create_macos_installer():
    """创建macOS安装包（使用create-dmg）"""
    subprocess.run([
        'create-dmg',
        '--volname', f'{BuildConfig.APP_NAME} {BuildConfig.APP_VERSION}',
        '--window-pos', '200', '120',
        '--window-size', '600', '300',
        '--icon-size', '100',
        '--icon', f'{BuildConfig.APP_NAME}.app', '175', '120',
        '--hide-extension', f'{BuildConfig.APP_NAME}.app',
        '--app-drop-link', '425', '120',
        f'{BuildConfig.APP_NAME}-{BuildConfig.APP_VERSION}.dmg',
        f'dist/{BuildConfig.APP_NAME}.app'
    ], check=True)

def create_linux_installer():
    """创建Linux安装包（AppImage）"""
    subprocess.run([
        'python', '-m', 'appimage',
        '--name', BuildConfig.APP_NAME,
        '--version', BuildConfig.APP_VERSION,
        '--icon', 'resources/icons/app.png',
        '--desktop-file', 'resources/linux/app.desktop',
        f'dist/{BuildConfig.APP_NAME}'
    ], check=True)
```

### 分发策略

**发布渠道**：
- GitHub Releases（主要分发渠道）
- 官方网站下载页面
- 学术机构内部分发

**版本管理**：
- 语义化版本控制（Semantic Versioning）
- 自动化版本标记和发布
- 变更日志自动生成

**更新机制**：
- 内置更新检查功能
- 增量更新支持
- 回滚机制

## 统一项目结构

```
colorectal-screening-model/
├── .github/                    # CI/CD工作流
│   └── workflows/
│       ├── ci.yaml
│       ├── build-desktop.yaml
│       └── release.yaml
├── src/                        # 主要源代码
│   ├── core/                   # 核心模拟引擎
│   │   ├── simulation/         # 微观模拟引擎
│   │   │   ├── population.py   # 人群队列管理
│   │   │   ├── individual.py   # 个体数据结构
│   │   │   └── engine.py       # 模拟引擎主控制器
│   │   ├── disease/            # 疾病建模模块
│   │   │   ├── adenoma_pathway.py      # 腺瘤-癌变通路（85%）
│   │   │   ├── serrated_pathway.py     # 锯齿状腺瘤通路（15%）
│   │   │   ├── risk_factors.py         # 风险因素管理
│   │   │   └── progression.py          # 疾病进展建模
│   │   ├── screening/          # 筛查策略模块
│   │   │   ├── tools/          # 筛查工具实现
│   │   │   │   ├── fit.py      # 粪便免疫化学检测
│   │   │   │   ├── colonoscopy.py      # 结肠镜检查
│   │   │   │   ├── sigmoidoscopy.py    # 乙状结肠镜检查
│   │   │   │   └── risk_assessment.py  # 风险评估问卷
│   │   │   ├── strategy.py     # 筛查策略设计器
│   │   │   ├── compliance.py   # 依从性建模
│   │   │   └── results.py      # 筛查结果处理
│   │   └── economics/          # 卫生经济学分析
│   │       ├── costs.py        # 成本建模系统
│   │       ├── outcomes.py     # 健康结果指标（QALY、LYG）
│   │       ├── analysis.py     # 成本效益分析（ICER）
│   │       └── sensitivity.py  # 敏感性分析
│   ├── calibration/            # 机器学习校准系统
│   │   ├── ml/                 # 机器学习模块
│   │   │   ├── networks.py     # 深度神经网络架构
│   │   │   ├── training.py     # 训练过程管理
│   │   │   └── optimization.py # 参数优化算法
│   │   ├── sampling/           # 参数抽样系统
│   │   │   ├── lhs.py          # 拉丁超立方抽样
│   │   │   ├── parameters.py   # 参数空间定义
│   │   │   └── constraints.py  # 参数约束检查
│   │   ├── targets/            # 校准目标管理
│   │   │   ├── data.py         # 基准值数据管理
│   │   │   ├── weights.py      # 校准权重设置
│   │   │   └── validation.py   # 校准结果验证
│   │   └── confidence/         # 置信区间计算
│   │       ├── bootstrap.py    # Bootstrap方法
│   │       ├── intervals.py    # 95%置信区间计算
│   │       └── uncertainty.py  # 不确定性分析
│   ├── data/                   # 数据管理模块
│   │   ├── import/             # 数据导入系统
│   │   │   ├── csv_parser.py   # CSV格式解析
│   │   │   ├── excel_parser.py # Excel格式解析
│   │   │   ├── json_parser.py  # JSON格式解析
│   │   │   └── validator.py    # 数据验证器
│   │   ├── management/         # 数据管理
│   │   │   ├── life_tables.py  # 生命表管理
│   │   │   ├── calibration_targets.py  # 校准基准值管理
│   │   │   ├── population_data.py      # 人口结构数据
│   │   │   └── screening_params.py     # 筛查工具参数
│   │   ├── quality/            # 数据质量检查
│   │   │   ├── completeness.py # 完整性检查
│   │   │   ├── consistency.py  # 一致性检查
│   │   │   └── reasonableness.py       # 合理性检查
│   │   └── version/            # 版本控制
│   │       ├── history.py      # 数据历史管理
│   │       ├── comparison.py   # 版本对比
│   │       └── rollback.py     # 回滚功能
│   ├── gui/                    # 桌面应用界面
│   │   ├── main_window.py      # 主窗口
│   │   ├── dashboard/          # 主仪表板
│   │   │   ├── overview.py     # 概览面板
│   │   │   ├── project_cards.py        # 项目状态卡片
│   │   │   └── system_status.py        # 系统状态面板
│   │   ├── configuration/      # 模拟配置界面
│   │   │   ├── wizard.py       # 6步配置向导
│   │   │   ├── population_setup.py     # 人群设置界面
│   │   │   ├── disease_modeling.py     # 疾病建模配置
│   │   │   ├── screening_strategy.py   # 筛查策略设计器
│   │   │   ├── economic_params.py      # 经济参数设置
│   │   │   └── calibration_setup.py    # 校准设置界面
│   │   ├── monitoring/         # 模拟监控界面
│   │   │   ├── progress.py     # 进度监控
│   │   │   ├── performance.py  # 性能监控
│   │   │   └── logs.py         # 日志查看器
│   │   ├── analysis/           # 结果分析界面
│   │   │   ├── dashboard.py    # 结果仪表板
│   │   │   ├── comparison.py   # 比较分析
│   │   │   ├── visualization.py        # 数据可视化
│   │   │   └── reports.py      # 报告生成
│   │   ├── data_management/    # 数据管理界面
│   │   │   ├── import_wizard.py        # 数据导入向导
│   │   │   ├── quality_check.py        # 数据质量检查界面
│   │   │   ├── version_control.py      # 版本控制界面
│   │   │   └── file_browser.py         # 文件浏览器
│   │   └── components/         # 可复用UI组件
│   │       ├── charts/         # 图表组件
│   │       │   ├── scientific_line.py  # 科学线图
│   │       │   ├── interactive_bar.py  # 交互式柱状图
│   │       │   ├── heatmap.py          # 热力图
│   │       │   └── scatter.py          # 散点图
│   │       ├── forms/          # 表单组件
│   │       │   ├── parameter_panel.py  # 参数配置面板
│   │       │   ├── data_upload.py      # 数据上传组件
│   │       │   └── validation.py       # 验证组件
│   │       └── layout/         # 布局组件
│   │           ├── sidebar.py  # 侧边栏
│   │           ├── toolbar.py  # 工具栏
│   │           └── status_bar.py       # 状态栏
│   ├── utils/                  # 工具模块
│   │   ├── config.py           # 配置管理
│   │   ├── logging.py          # 日志系统
│   │   ├── validation.py       # 通用验证
│   │   ├── performance.py      # 性能监控
│   │   └── file_utils.py       # 文件操作工具
│   └── main.py                 # 应用入口点
├── data/                       # 数据文件
│   ├── life_tables/            # 生命表数据
│   │   ├── china_national/     # 中国国家级数据
│   │   ├── provinces/          # 省级数据
│   │   └── cities/             # 城市级数据
│   ├── calibration_targets/    # 校准基准值
│   │   ├── adenoma_prevalence/ # 腺瘤患病率
│   │   ├── cancer_incidence/   # 癌症发病率
│   │   └── mortality_rates/    # 死亡率
│   ├── population/             # 人口结构数据
│   │   ├── demographics/       # 人口统计学数据
│   │   └── risk_factors/       # 风险因素分布
│   └── screening_tools/        # 筛查工具参数
│       ├── fit_parameters/     # FIT参数
│       ├── colonoscopy_params/ # 结肠镜参数
│       └── other_tools/        # 其他工具参数
├── tests/                      # 测试套件
│   ├── unit/                   # 单元测试
│   │   ├── core/               # 核心模块测试
│   │   ├── calibration/        # 校准模块测试
│   │   ├── data/               # 数据模块测试
│   │   └── gui/                # GUI模块测试
│   ├── integration/            # 集成测试
│   │   ├── simulation_workflow/        # 模拟工作流测试
│   │   ├── data_pipeline/      # 数据管道测试
│   │   └── calibration_process/        # 校准流程测试
│   ├── performance/            # 性能测试
│   │   ├── large_population/   # 大规模人群测试
│   │   ├── long_simulation/    # 长期模拟测试
│   │   └── memory_usage/       # 内存使用测试
│   └── validation/             # 验证测试
│       ├── epidemiological/    # 流行病学验证
│       ├── economic/           # 经济学验证
│       └── clinical/           # 临床验证
├── docs/                       # 文档
│   ├── prd.md                  # 产品需求文档
│   ├── front-end-spec.md       # 前端设计规格
│   ├── architecture.md         # 架构文档
│   ├── user_guide/             # 用户指南
│   │   ├── installation.md     # 安装指南
│   │   ├── configuration.md    # 配置指南
│   │   └── tutorials/          # 教程
│   ├── developer_guide/        # 开发者指南
│   │   ├── setup.md            # 开发环境设置
│   │   ├── contributing.md     # 贡献指南
│   │   └── api_reference/      # API参考
│   └── scientific/             # 科学文档
│       ├── model_description.md        # 模型描述
│       ├── validation_results.md       # 验证结果
│       └── references.md       # 参考文献
├── scripts/                    # 构建和部署脚本
│   ├── build_desktop.py        # 桌面应用构建
│   ├── package_installer.py    # 安装包生成
│   ├── run_tests.py            # 测试运行脚本
│   └── setup_dev.py            # 开发环境设置
├── requirements/               # 依赖管理
│   ├── base.txt                # 基础依赖
│   ├── gui.txt                 # GUI依赖
│   ├── ml.txt                  # 机器学习依赖
│   ├── dev.txt                 # 开发依赖
│   └── test.txt                # 测试依赖
├── config/                     # 配置文件
│   ├── default.yaml            # 默认配置
│   ├── development.yaml        # 开发配置
│   ├── production.yaml         # 生产配置
│   └── logging.yaml            # 日志配置
├── .env.example                # 环境变量模板
├── .gitignore
├── pyproject.toml              # Poetry配置
├── setup.py                    # 安装脚本
└── README.md
```

## 开发工作流

### 本地开发设置

#### 前置条件

```bash
# 安装Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装Python 3.9+
sudo apt-get update
sudo apt-get install python3.9 python3.9-venv python3.9-dev

# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 安装Nx CLI
npm install -g nx@latest
```

#### 初始设置

```bash
# 克隆仓库
git clone https://github.com/your-org/colorectal-screening-model.git
cd colorectal-screening-model

# 安装依赖
npm install

# 设置Python虚拟环境
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 复制环境变量
cp .env.example .env
# 编辑.env文件，填入必要的配置

# 启动开发数据库
docker-compose -f infrastructure/docker-compose/docker-compose.dev.yml up -d postgres redis minio

# 运行数据库迁移
nx run api-gateway:migrate

# 安装前端依赖
nx run web:install
```

#### 开发命令

```bash
# 启动所有服务
nx run-many --target=serve --all

# 启动前端应用
nx serve web

# 启动API网关
nx serve api-gateway

# 启动模拟服务
nx serve simulation-service

# 启动校准服务
nx serve calibration-service

# 运行测试
nx run-many --target=test --all

# 构建所有应用
nx run-many --target=build --all

# 代码检查
nx run-many --target=lint --all

# 类型检查
nx run-many --target=type-check --all
```

### 部署配置

#### 部署平台

**前端部署**：
- **平台**：Vercel / Netlify
- **构建命令**：`nx build web`
- **部署方法**：Git集成自动部署

**后端部署**：
- **平台**：Kubernetes集群 (AWS EKS / Google GKE)
- **构建命令**：Docker镜像构建
- **部署方法**：GitOps (ArgoCD)

#### CI/CD流水线

```yaml
# .github/workflows/ci.yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'

      - name: Install dependencies
        run: |
          npm ci
          pip install -r requirements.txt

      - name: Run tests
        run: |
          nx run-many --target=test --all --parallel

      - name: Run linting
        run: |
          nx run-many --target=lint --all --parallel

      - name: Type checking
        run: |
          nx run-many --target=type-check --all --parallel

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push images
        run: |
          nx run-many --target=docker-build --all --parallel
          nx run-many --target=docker-push --all --parallel

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Deploy to staging
        run: |
          # 部署到测试环境的脚本
          ./scripts/deploy.sh staging

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Deploy to production
        run: |
          # 部署到生产环境的脚本
          ./scripts/deploy.sh production
```

#### 环境配置

| 环境 | 前端URL | 后端URL | 用途 |
|------|---------|---------|------|
| 开发环境 | http://localhost:3000 | http://localhost:8000 | 本地开发 |
| 测试环境 | https://staging.colorectal-screening.org | https://api-staging.colorectal-screening.org | 预生产测试 |
| 生产环境 | https://colorectal-screening.org | https://api.colorectal-screening.org | 生产环境 |

## 测试策略

### 测试金字塔

```
                  E2E Tests
                 /        \
            Integration Tests
               /            \
          Frontend Unit  Backend Unit
```

### 测试组织

#### 前端测试

```
apps/web/tests/
├── unit/                      # 单元测试
│   ├── components/
│   │   ├── Button.test.tsx
│   │   ├── Chart.test.tsx
│   │   └── Form.test.tsx
│   ├── hooks/
│   │   ├── useSimulation.test.ts
│   │   └── useCalibration.test.ts
│   ├── services/
│   │   ├── api.test.ts
│   │   └── simulation.test.ts
│   └── utils/
│       ├── validation.test.ts
│       └── calculations.test.ts
├── integration/               # 集成测试
│   ├── pages/
│   │   ├── Dashboard.test.tsx
│   │   └── SimulationConfig.test.tsx
│   └── features/
│       ├── simulation-flow.test.tsx
│       └── data-upload.test.tsx
└── e2e/                      # 端到端测试
    ├── simulation-workflow.spec.ts
    ├── calibration-process.spec.ts
    └── results-analysis.spec.ts
```

#### 后端测试

```
apps/simulation-service/tests/
├── unit/                      # 单元测试
│   ├── core/
│   │   ├── test_population.py
│   │   ├── test_disease.py
│   │   └── test_screening.py
│   ├── services/
│   │   ├── test_project_service.py
│   │   └── test_simulation_service.py
│   └── utils/
│       ├── test_validation.py
│       └── test_calculations.py
├── integration/               # 集成测试
│   ├── test_api_endpoints.py
│   ├── test_database_operations.py
│   └── test_task_queue.py
└── performance/               # 性能测试
    ├── test_large_population.py
    ├── test_long_simulation.py
    └── test_concurrent_users.py
```

### 测试示例

#### 前端单元测试

```typescript
// apps/web/tests/unit/components/SimulationProgress.test.tsx
import { render, screen } from '@testing-library/react';
import { SimulationProgress } from '@/components/SimulationProgress';

describe('SimulationProgress', () => {
  it('displays correct progress percentage', () => {
    render(
      <SimulationProgress
        currentYear={50}
        totalYears={100}
        individualsProcessed={500000}
        totalIndividuals={1000000}
      />
    );

    expect(screen.getByText('50%')).toBeInTheDocument();
    expect(screen.getByText('Year 50 of 100')).toBeInTheDocument();
  });

  it('shows estimated completion time', () => {
    const estimatedCompletion = new Date(Date.now() + 3600000); // 1 hour from now

    render(
      <SimulationProgress
        currentYear={25}
        totalYears={100}
        estimatedCompletion={estimatedCompletion}
      />
    );

    expect(screen.getByText(/Estimated completion/)).toBeInTheDocument();
  });
});
```

#### 后端单元测试

```python
# apps/simulation-service/tests/unit/core/test_population.py
import pytest
from src.core.population import PopulationManager, Individual
from src.models.population import PopulationConfig

class TestPopulationManager:
    def test_initialize_population(self):
        config = PopulationConfig(
            size=1000,
            age_distribution={'min_age': 50, 'max_age': 80},
            gender_ratio=0.5
        )

        manager = PopulationManager(config)
        population = manager.initialize_population()

        assert len(population) == 1000
        assert all(50 <= individual.age <= 80 for individual in population)

        male_count = sum(1 for individual in population if individual.gender == 'male')
        female_count = len(population) - male_count

        # 允许5%的误差
        assert abs(male_count - female_count) <= 50

    def test_apply_mortality(self):
        config = PopulationConfig(size=1000)
        manager = PopulationManager(config)
        population = manager.initialize_population()

        initial_count = len(population)
        manager.apply_annual_mortality(population)

        # 应该有一些个体死亡
        assert len(population) < initial_count

        # 所有存活个体年龄应该增加1
        assert all(individual.age > 50 for individual in population)

    @pytest.mark.performance
    def test_large_population_performance(self):
        """测试大规模人群的性能"""
        import time

        config = PopulationConfig(size=100000)
        manager = PopulationManager(config)

        start_time = time.time()
        population = manager.initialize_population()
        end_time = time.time()

        # 10万人群初始化应该在5秒内完成
        assert end_time - start_time < 5.0
        assert len(population) == 100000
```

#### 集成测试

```python
# apps/simulation-service/tests/integration/test_simulation_workflow.py
import pytest
from httpx import AsyncClient
from src.main import app

@pytest.mark.asyncio
class TestSimulationWorkflow:
    async def test_complete_simulation_workflow(self):
        """测试完整的模拟工作流"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            # 1. 创建项目
            project_data = {
                "name": "Test Simulation",
                "description": "Integration test simulation"
            }
            response = await client.post("/projects", json=project_data)
            assert response.status_code == 201
            project = response.json()
            project_id = project["id"]

            # 2. 配置模拟参数
            config_data = {
                "population": {
                    "size": 1000,
                    "age_distribution": {"min_age": 50, "max_age": 80}
                },
                "disease_model": {
                    "adenoma_pathway_rate": 0.85
                },
                "screening_strategy": {
                    "tools": [{
                        "type": "fit",
                        "start_age": 50,
                        "end_age": 75,
                        "interval": 2
                    }]
                }
            }
            response = await client.put(
                f"/projects/{project_id}/configuration",
                json=config_data
            )
            assert response.status_code == 200

            # 3. 启动模拟
            response = await client.post(f"/projects/{project_id}/simulations")
            assert response.status_code == 202
            simulation = response.json()
            simulation_id = simulation["id"]

            # 4. 检查模拟状态
            response = await client.get(f"/simulations/{simulation_id}/status")
            assert response.status_code == 200
            status = response.json()
            assert status["status"] in ["queued", "running"]
```

## 监控和可观测性

### 监控架构

```mermaid
graph TB
    subgraph "应用层"
        APP[应用服务]
        WEB[Web应用]
    end

    subgraph "指标收集"
        PROM[Prometheus]
        JAEGER[Jaeger]
        ELK[ELK Stack]
    end

    subgraph "可视化"
        GRAFANA[Grafana]
        KIBANA[Kibana]
    end

    subgraph "告警"
        ALERT[AlertManager]
        SLACK[Slack通知]
        EMAIL[邮件通知]
    end

    APP --> PROM
    APP --> JAEGER
    APP --> ELK
    WEB --> PROM

    PROM --> GRAFANA
    ELK --> KIBANA
    JAEGER --> GRAFANA

    PROM --> ALERT
    ALERT --> SLACK
    ALERT --> EMAIL
```

### 关键指标

#### 应用指标

```python
# 应用指标收集
from prometheus_client import Counter, Histogram, Gauge
import time

# 请求计数器
REQUEST_COUNT = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status']
)

# 请求延迟
REQUEST_LATENCY = Histogram(
    'http_request_duration_seconds',
    'HTTP request latency',
    ['method', 'endpoint']
)

# 活跃模拟数量
ACTIVE_SIMULATIONS = Gauge(
    'active_simulations_total',
    'Number of active simulations'
)

# 模拟进度
SIMULATION_PROGRESS = Gauge(
    'simulation_progress_percent',
    'Simulation progress percentage',
    ['simulation_id']
)

# 中间件示例
@app.middleware("http")
async def metrics_middleware(request: Request, call_next):
    start_time = time.time()

    response = await call_next(request)

    # 记录指标
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.url.path,
        status=response.status_code
    ).inc()

    REQUEST_LATENCY.labels(
        method=request.method,
        endpoint=request.url.path
    ).observe(time.time() - start_time)

    return response
```

#### 业务指标

- **模拟性能指标**：
  - 模拟完成时间
  - 每秒处理的个体数量
  - 内存使用效率
  - GPU利用率

- **用户行为指标**：
  - 活跃用户数
  - 项目创建率
  - 模拟成功率
  - 功能使用频率

- **系统健康指标**：
  - API响应时间
  - 错误率
  - 数据库连接池状态
  - 队列长度

### 告警规则

```yaml
# Prometheus告警规则
groups:
  - name: simulation-service
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} requests per second"

      - alert: SimulationStuck
        expr: increase(simulation_progress_percent[30m]) == 0
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Simulation appears to be stuck"
          description: "Simulation {{ $labels.simulation_id }} has not progressed in 30 minutes"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 / 1024 > 8
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}GB"

      - alert: DatabaseConnectionPoolExhausted
        expr: db_connection_pool_active >= db_connection_pool_max
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Database connection pool exhausted"
          description: "All database connections are in use"
```

### 日志管理

```python
# 结构化日志配置
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }

        # 添加额外字段
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'simulation_id'):
            log_entry['simulation_id'] = record.simulation_id
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id

        return json.dumps(log_entry)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('/var/log/app/application.log')
    ]
)

# 为每个服务配置专用logger
logger = logging.getLogger('simulation-service')
logger.addHandler(logging.StreamHandler())
logger.handlers[0].setFormatter(JSONFormatter())
```

---

**文档状态**：架构设计完成，等待技术评审和实施规划
